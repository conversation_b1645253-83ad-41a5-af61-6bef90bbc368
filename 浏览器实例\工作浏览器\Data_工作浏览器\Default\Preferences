{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "cmn-Hans-CN"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "autocomplete": {"retention_policy_last_version": 138}, "autofill": {"last_version_deduped": 138}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 682, "left": 6, "maximized": false, "right": 637, "top": 6, "work_area_bottom": 912, "work_area_left": 0, "work_area_right": 1440, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21843, "default_apps_install_state": 2, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "326dcad7-de68-477c-84e6-25b9aff70608", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "commands": {"windows:Alt+K": {"command_name": "commands_modal", "extension": "mnpdbmgpebfihcndnpgdaihnkmloclkd", "global": false}, "windows:Alt+Q": {"command_name": "advanced_select", "extension": "mnpdbmgpebfihcndnpgdaihnkmloclkd", "global": false}, "windows:Alt+S": {"command_name": "summary_page", "extension": "mnpdbmgpebfihcndnpgdaihnkmloclkd", "global": false}, "windows:Alt+T": {"command_name": "web_translate", "extension": "mnpdbmgpebfihcndnpgdaihnkmloclkd", "global": false}}, "cws_info_timestamp": "13397915416868441", "install_signature": {"expire_date": "2025-10-17", "ids": ["kebpgmmmoiggnchlpamiefihdjiaikaf", "ljglajjnnkapghbckkcmodicjhacbfhk", "mnpdbmgpebfihcndnpgdaihnkmloclkd", "ncennffkjdiamlpmcbajkmaiiiddgioo", "ngpampappnmepgilojfohadhhmbhlaek"], "invalid_ids": [], "salt": "qN2JFxiup8xeBTM6sPgHwE47XFo6oJZADHneV8iForY=", "signature": "PoiF0UKrJG1GBCZHBJiMW0URvmvlEkKKmc633G/1ABkZQkz9Qf/VSN1ms2USR04fh4YKdxfICEpa6f7Pi3n7HwLC23exx9J+dk0f43DIipflKy/MQRxuu7wXpgHkzYnTsHQYRAT2ytpuGi9GLnqzFgrE39NT6RNzBtgXVE/oCQuK6C5OXbl3JnaBhC3Xux0RnA/QtUShdrYp2Ua3oO7QydfljkcJuMhY9+Kyaxv50gcNeL2Uyoa/Vc7HN8yoFUH5IpzxlI5KH7LSr0SJ+aJf9Vd1un3alT/8ZhWfu6yFyOjv9L+1UQfMe4zF7VdVD/rrZ0NR6zx8ZLNKLfPg+5zLAw==", "signature_format_version": 2, "timestamp": "*****************"}, "last_chrome_version": "138.0.7204.158"}, "gaia_cookie": {"changed_time": **********.940323, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_binary_data": ""}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "a9d80bc2-c1b9-4eea-9c62-b1f31d47e2be"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "PasswordManualFallbackAvailable": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "zh-CN,zh"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "ctkzlkVDTmGs1nvl0YEZ8ZWQb8UdcGpAPURwEwDqmL6KjKqJxMNWLRfM2bylK2nepKrgvb5KW363RZBUyr8fiQ=="}, "migrated_user_scripts_toggle": true, "ntp": {"num_personal_suggestions": 1}, "optimization_guide": {"predictionmodelfetcher": {"last_fetch_attempt": "*****************", "last_fetch_success": "*****************"}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_TRACKING": true, "SAVED_TAB_GROUP": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 6, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "13397915251871160", "setting": {"lastEngagementTime": 1.339791525187114e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 9.0, "rawScore": 9.0}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "138.0.7204.158", "creation_time": "13397912778380129", "exit_type": "SessionEnded", "family_member_role": "not_in_family", "last_engagement_time": "13397915251871139", "last_time_obsolete_http_credentials_removed": 1753440967.920329, "last_time_password_store_metrics_reported": 1753440937.909473, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "您的 Chrome", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "hash_real_time_ohttp_expiration_time": "13398171980109608", "hash_real_time_ohttp_key": "2AAgETtEl5cjzDpksr9J2MiGvUsUaS87sxbZ6vEn9Mxqv18ABAABAAI=", "metrics_last_log_time": "13397912778", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQqPqu+tCq5hcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEOv6rvrQquYX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13397788799000000", "uma_in_sql_start_time": "13397912778424866"}, "sessions": {"event_log": [{"crashed": false, "time": "13397912778422517", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397912780004666", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397913259913976", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397913261972465", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397913303442070", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397913304770765", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397914507905243", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397914606360895", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397915251609754", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397919333909451", "type": 2, "window_count": 1}], "session_data_status": 1}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["zh-CN", "en-US"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "updateclientdata": {"apps": {"kebpgmmmoiggnchlpamiefihdjiaikaf": {"cohort": "1::", "cohortname": "", "dlrc": 6780, "installdate": 6780, "pf": "b2318020-7aa3-449d-bec8-186505001e6f"}, "ljglajjnnkapghbckkcmodicjhacbfhk": {"cohort": "1::", "cohortname": "", "dlrc": 6780, "installdate": 6780, "pf": "0262112b-a340-4cb2-bb59-9c5d0d46cfa8"}, "mnpdbmgpebfihcndnpgdaihnkmloclkd": {"cohort": "1::", "cohortname": "", "dlrc": 6780, "fp": "2.1.0.24", "installdate": 6780, "max_pv": "1.0.24", "pf": "dfea1aec-199d-4f93-8d7a-07ab7fe98324", "pv": "1.1.1"}, "ncennffkjdiamlpmcbajkmaiiiddgioo": {"cohort": "1::", "cohortname": "", "dlrc": 6780, "fp": "2.3.52.5", "installdate": 6780, "max_pv": "3.52.5", "pf": "698728a6-3f72-4226-875a-db3c090b8edd", "pv": "3.52.10"}, "ngpampappnmepgilojfohadhhmbhlaek": {"cohort": "1::", "cohortname": "", "dlrc": 6780, "fp": "2.6.42.32", "installdate": 6780, "max_pv": "6.42.32", "pf": "b61fa4fc-c362-4939-9577-1708d7f11cf7", "pv": "6.42.42"}, "nmmhkkegccagdldgiimedpiccmgmieda": {"cohort": "1::", "cohortname": "", "dlrc": 6780, "installdate": 6780, "pf": "00e5708c-9190-4ce0-9475-bd64da4a344c"}}}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "138", "migrated_default_apps": ["aohghmighlieiainnegkcijnfilokake", "aapocclcgogkmnckokdopfmhonfmgoek", "felcaaldnbdncclmgdcncolpebgiejap", "apdfllckaahabafndbhieahigkjlhalf", "pjkljhegncpnkpknbcohdijeoejaedia", "blpcfgokakmgnkcojhhkbfbldkacnbeo"]}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[],[],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:suggesteventid\":\"1467460362325077564\",\"google:suggesttype\":[],\"google:verbatimrelevance\":851}]"}}