#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 - 浏览器实例修复工具
版本: v2.2.1
作者: 浏览器多账号绿色版团队
描述: 修复现有浏览器实例的启动脚本和快捷方式问题
"""

import sys
import shutil
from pathlib import Path
from 浏览器管理器 import 浏览器管理器

def 修复单个浏览器实例(管理器: 浏览器管理器, 浏览器名称: str, 实例目录: Path) -> bool:
    """修复单个浏览器实例"""
    try:
        print(f"🔧 修复浏览器实例: {浏览器名称}")
        
        # 检查基本文件结构
        chrome_bin = 实例目录 / "Chrome-bin"
        数据目录 = 实例目录 / f"Data_{浏览器名称}"
        chrome_exe = chrome_bin / "chrome.exe"
        
        if not chrome_bin.exists():
            print(f"  ❌ Chrome-bin目录不存在: {chrome_bin}")
            return False
            
        if not chrome_exe.exists():
            print(f"  ❌ Chrome可执行文件不存在: {chrome_exe}")
            return False
            
        if not 数据目录.exists():
            print(f"  ❌ 数据目录不存在: {数据目录}")
            return False
        
        print(f"  ✅ 基本文件结构正常")
        
        # 创建本地启动脚本
        启动脚本路径 = 实例目录 / f"{浏览器名称}.bat"
        启动命令 = f'"{chrome_exe}" --user-data-dir="{数据目录}" --no-first-run --no-default-browser-check'
        
        try:
            with open(启动脚本路径, 'w', encoding='utf-8') as f:
                f.write(f'@echo off\n')
                f.write(f'cd /d "{实例目录}"\n')
                f.write(f'{启动命令}\n')
            
            print(f"  ✅ 创建本地启动脚本: {启动脚本路径}")
        except Exception as e:
            print(f"  ❌ 创建本地启动脚本失败: {e}")
            return False
        
        # 尝试创建桌面快捷方式
        try:
            # 查找图标类型
            图标类型 = "chrome"  # 默认
            for 扩展名 in ['.ico', '.png', '.jpg']:
                for 图标文件 in 实例目录.glob(f"*{扩展名}"):
                    if 图标文件.stem in ['chrome', 'firefox', 'edge', 'generic']:
                        图标类型 = 图标文件.stem
                        break
                if 图标类型 != "chrome":
                    break
            
            成功 = 管理器.快捷方式管理器实例.创建浏览器快捷方式(
                浏览器名称=浏览器名称,
                实例目录=实例目录,
                图标类型=图标类型
            )
            
            if 成功:
                print(f"  ✅ 桌面快捷方式创建成功")
            else:
                print(f"  ⚠️ 桌面快捷方式创建失败")
                
        except Exception as e:
            print(f"  ⚠️ 桌面快捷方式创建异常: {e}")
        
        print(f"  🎉 浏览器实例修复完成: {浏览器名称}")
        return True
        
    except Exception as e:
        print(f"  ❌ 修复浏览器实例异常: {e}")
        return False

def 修复所有浏览器实例():
    """修复所有浏览器实例"""
    print("🔧 开始修复所有浏览器实例...")
    
    try:
        管理器 = 浏览器管理器()
        浏览器列表 = 管理器.获取浏览器列表()
        
        if not 浏览器列表:
            print("📋 没有找到浏览器实例")
            return True
        
        print(f"📋 找到 {len(浏览器列表)} 个浏览器实例")
        
        修复成功数 = 0
        修复失败数 = 0
        
        for 浏览器 in 浏览器列表:
            浏览器名称 = 浏览器['名称']
            实例目录 = Path(浏览器['路径'])
            
            if 修复单个浏览器实例(管理器, 浏览器名称, 实例目录):
                修复成功数 += 1
            else:
                修复失败数 += 1
        
        print(f"\n📊 修复统计:")
        print(f"  总实例数: {len(浏览器列表)}")
        print(f"  修复成功: {修复成功数}")
        print(f"  修复失败: {修复失败数}")
        
        if 修复失败数 == 0:
            print(f"\n🎉 所有浏览器实例修复成功！")
            return True
        else:
            print(f"\n⚠️ 有 {修复失败数} 个实例修复失败")
            return False
            
    except Exception as e:
        print(f"❌ 修复过程中发生错误: {e}")
        return False

def 测试修复效果():
    """测试修复效果"""
    print("\n🧪 测试修复效果...")
    
    try:
        管理器 = 浏览器管理器()
        浏览器列表 = 管理器.获取浏览器列表()
        
        if not 浏览器列表:
            print("📋 没有找到浏览器实例")
            return True
        
        测试成功数 = 0
        测试失败数 = 0
        
        for 浏览器 in 浏览器列表:
            浏览器名称 = 浏览器['名称']
            实例目录 = Path(浏览器['路径'])
            
            print(f"🧪 测试浏览器实例: {浏览器名称}")
            
            # 检查启动脚本是否存在
            启动脚本路径 = 实例目录 / f"{浏览器名称}.bat"
            if 启动脚本路径.exists():
                print(f"  ✅ 启动脚本存在: {启动脚本路径}")
                测试成功数 += 1
            else:
                print(f"  ❌ 启动脚本缺失: {启动脚本路径}")
                测试失败数 += 1
        
        print(f"\n📊 测试统计:")
        print(f"  总实例数: {len(浏览器列表)}")
        print(f"  测试通过: {测试成功数}")
        print(f"  测试失败: {测试失败数}")
        
        if 测试失败数 == 0:
            print(f"\n🎉 所有浏览器实例测试通过！")
            return True
        else:
            print(f"\n⚠️ 有 {测试失败数} 个实例测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def 清理无效实例():
    """清理无效的浏览器实例"""
    print("\n🧹 检查并清理无效实例...")
    
    try:
        管理器 = 浏览器管理器()
        浏览器列表 = 管理器.获取浏览器列表()
        
        if not 浏览器列表:
            print("📋 没有找到浏览器实例")
            return True
        
        无效实例 = []
        
        for 浏览器 in 浏览器列表:
            浏览器名称 = 浏览器['名称']
            实例目录 = Path(浏览器['路径'])
            
            # 检查关键文件
            chrome_exe = 实例目录 / "Chrome-bin" / "chrome.exe"
            数据目录 = 实例目录 / f"Data_{浏览器名称}"
            
            if not chrome_exe.exists() or not 数据目录.exists():
                无效实例.append((浏览器名称, 实例目录))
                print(f"⚠️ 发现无效实例: {浏览器名称}")
        
        if not 无效实例:
            print("✅ 没有发现无效实例")
            return True
        
        print(f"\n发现 {len(无效实例)} 个无效实例:")
        for 浏览器名称, 实例目录 in 无效实例:
            print(f"  - {浏览器名称}: {实例目录}")
        
        确认 = input(f"\n是否删除这些无效实例？(y/N): ")
        if 确认.lower() == 'y':
            删除成功数 = 0
            for 浏览器名称, 实例目录 in 无效实例:
                try:
                    if 实例目录.exists():
                        shutil.rmtree(实例目录)
                        print(f"✅ 删除无效实例: {浏览器名称}")
                        删除成功数 += 1
                except Exception as e:
                    print(f"❌ 删除失败: {浏览器名称} - {e}")
            
            print(f"\n🎉 成功删除 {删除成功数} 个无效实例")
        else:
            print("❌ 用户取消删除操作")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    print("🔧 浏览器多账号绿色版 - 浏览器实例修复工具")
    print("版本: v2.2.1")
    print("="*60)
    
    try:
        # 修复所有浏览器实例
        修复成功 = 修复所有浏览器实例()
        
        # 测试修复效果
        测试成功 = 测试修复效果()
        
        # 清理无效实例
        清理成功 = 清理无效实例()
        
        if 修复成功 and 测试成功:
            print(f"\n🎉 浏览器实例修复完成！所有实例现在应该可以正常使用。")
            return 0
        else:
            print(f"\n⚠️ 修复过程中遇到问题，请检查上述错误信息。")
            return 1
            
    except KeyboardInterrupt:
        print("\n\n👋 用户中断修复过程")
        return 1
    except Exception as e:
        print(f"\n❌ 修复过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
